#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消融实验结果提取工具
处理消融实验文件夹下的三个模型结果，生成Excel表格和图像
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置区域
BASE_FOLDERS = [
    '消融实验/完整模型',
    '消融实验/单元换LSTM',
    '消融实验/双向LSTM换卷积层'
]

def find_latest_folder(base_path, prefix_filter=None):
    """查找最新的文件夹（按时间戳排序）"""
    if not os.path.exists(base_path):
        return None
    
    folders = []
    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path):
            if prefix_filter is None or item.startswith(prefix_filter):
                folders.append(item)
    
    if not folders:
        return None
    
    # 按时间戳排序，选择最新的
    folders.sort(reverse=True)
    return folders[0]

def extract_snr_data(model_path):
    """从模型文件夹提取各SNR准确率"""
    model_name = os.path.basename(model_path)
    print(f"    正在提取 {model_name} 的SNR数据...")

    # 查找测试文件夹（以test_开头的文件夹）
    test_folder = find_latest_folder(model_path, 'test_')
    if not test_folder:
        print(f"      ❌ 未找到测试文件夹 (test_开头的文件夹)")
        return None

    print(f"      📁 找到测试文件夹: {test_folder}")

    # 读取snr_results.json
    snr_results_path = os.path.join(model_path, test_folder, 'plots', 'snr_results.json')
    if not os.path.exists(snr_results_path):
        print(f"      ❌ SNR结果文件不存在: {snr_results_path}")
        return None

    try:
        with open(snr_results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 提取SNR数据，格式为 {SNR值: 准确率}
        snr_data = {}
        snr_count = 0
        for snr_key, metrics in data.items():
            if snr_key.startswith('SNR_'):
                # 提取SNR值，例如从 'SNR_-20dB' 提取 '-20dB'
                snr_value = snr_key.replace('SNR_', '')
                accuracy = metrics.get('accuracy', 0)
                snr_data[snr_value] = accuracy
                snr_count += 1

        print(f"      ✅ 成功读取SNR数据:")
        print(f"         📄 文件: {snr_results_path}")
        print(f"         📊 SNR数据点数量: {snr_count}")
        print(f"         📊 SNR范围: {list(snr_data.keys())}")

        return snr_data

    except Exception as e:
        print(f"      ❌ 读取SNR结果文件失败: {snr_results_path}")
        print(f"         错误: {e}")
        return None

def get_all_snr_values():
    """获取所有SNR值的并集"""
    all_snr_values = set()
    
    for base_folder in BASE_FOLDERS:
        if os.path.exists(base_folder):
            snr_data = extract_snr_data(base_folder)
            if snr_data:
                all_snr_values.update(snr_data.keys())
    
    # 按SNR值排序
    snr_list = list(all_snr_values)
    snr_list.sort(key=lambda x: int(x.replace('dB', '')))
    return snr_list

def create_snr_table():
    """创建SNR准确率表格"""
    # 获取所有SNR值
    all_snr_values = get_all_snr_values()
    if not all_snr_values:
        print(f"警告: 未找到SNR数据")
        return None
    
    # 创建列名：模型名称 + 所有SNR值
    columns = ['模型名称'] + all_snr_values
    
    rows = []
    
    # 模型名称映射
    model_name_mapping = {
        '完整模型': 'MWRNN',
        '单元换LSTM': 'MWRNN-1',
        '双向LSTM换卷积层': 'MWRNN-2'
    }
    
    for base_folder in BASE_FOLDERS:
        if not os.path.exists(base_folder):
            continue
            
        folder_name = os.path.basename(base_folder)
        display_name = model_name_mapping.get(folder_name, folder_name)
        
        row = [display_name]  # 第一列是模型名称
        
        # 提取SNR数据
        snr_data = extract_snr_data(base_folder)
        
        if snr_data:
            # 为每个SNR值填入对应的准确率
            for snr_value in all_snr_values:
                accuracy = snr_data.get(snr_value, '')  # 如果没有该SNR值，填入空字符串
                row.append(accuracy)
        else:
            # 没有SNR数据，所有SNR列填入空字符串
            row.extend([''] * len(all_snr_values))
        
        rows.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(rows, columns=columns)
    return df

def create_snr_accuracy_plot(snr_df):
    """创建SNR准确率曲线图（完全参考plot_snr_results.py格式）"""
    if snr_df is None or snr_df.empty:
        print(f"警告: SNR表格为空，无法创建图表")
        return

    # 设置Times New Roman字体（完全参考原代码）
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['font.size'] = 12

    # 字体大小配置（完全参考原代码）
    FONT_CONFIG = {
        'axis_labels': 20,      # X and Y axis labels
        'legend': 18,           # Legend text
        'tick_labels': 18,      # Tick labels
    }

    # 提取SNR列（排除模型名称列）
    snr_columns = [col for col in snr_df.columns if col != '模型名称']
    if not snr_columns:
        print(f"警告: 未找到SNR列")
        return

    # 转换SNR值为数值（用于排序和绘图）
    snr_values = []
    for snr_col in snr_columns:
        try:
            # 从 '-20dB' 提取 -20
            snr_num = int(snr_col.replace('dB', ''))
            snr_values.append(snr_num)
        except:
            snr_values.append(0)

    # 按SNR值排序
    sorted_indices = np.argsort(snr_values)
    sorted_snr_values = [snr_values[i] for i in sorted_indices]
    sorted_snr_columns = [snr_columns[i] for i in sorted_indices]

    # 创建图表（IEEE journal格式 4:3比例）
    plt.figure(figsize=(8, 6))

    # 定义颜色和样式（完全参考原代码的PLOT_STYLES）
    plot_styles = [
        {'marker': 'o', 'linestyle': '-', 'color': '#d62728'},   # Red - MWRNN
        {'marker': 'D', 'linestyle': ':', 'color': '#1f77b4'},   # Blue - MWRNN-1
        {'marker': 's', 'linestyle': '--', 'color': '#ff7f0e'},  # Orange - MWRNN-2
    ]

    for idx, row in snr_df.iterrows():
        model_name = row['模型名称']

        accuracies = []
        valid_snr_values = []

        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            acc = row[snr_col]
            if pd.notna(acc) and acc != '':
                try:
                    # 转换为0-1范围（完全参考原代码格式）
                    acc_decimal = float(acc) / 100.0
                    accuracies.append(acc_decimal)
                    valid_snr_values.append(snr_val)
                except:
                    continue

        if not accuracies:
            print(f"警告: {model_name} 没有有效的准确率数据")
            continue

        # 获取样式 - MWRNN用红色圆点，其他两条区分
        if model_name == 'MWRNN':
            style = {'marker': 'o', 'linestyle': '-', 'color': '#d62728'}  # 红色圆点
        else:
            style = plot_styles[idx % len(plot_styles)]

        # 绘制曲线（完全参考原代码样式）
        plt.plot(valid_snr_values, accuracies,
                marker=style['marker'],
                linestyle=style['linestyle'],
                color=style['color'],
                label=model_name,
                linewidth=2,
                markersize=6)

    # 设置图表属性（完全参考原代码）
    plt.xlabel('SNR (dB)', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    plt.ylabel('Accuracy', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    # 不设置标题（参考原代码）
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper left', fontsize=FONT_CONFIG['legend'], frameon=True, fancybox=False, shadow=False)

    # 设置y轴格式（完全参考原代码）
    # 根据数据类型设置不同的y轴起始位置
    # 检查数据的最小值来决定起始位置
    all_accuracies = []
    for idx, row in snr_df.iterrows():
        for snr_col in sorted_snr_columns:
            acc = row[snr_col]
            if pd.notna(acc) and acc != '':
                try:
                    acc_decimal = float(acc) / 100.0
                    all_accuracies.append(acc_decimal)
                except:
                    continue

    if all_accuracies:
        min_acc = min(all_accuracies)
        if min_acc > 0.3:  # 如果最小准确率大于0.3，从0.35开始
            plt.ylim(0.35, 1.05)
        else:
            plt.ylim(0, 1.05)
    else:
        plt.ylim(0, 1.05)  # 默认从0开始
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1f}'.format(y)))

    # 设置刻度参数（完全参考原代码）
    plt.tick_params(axis='both', which='major', labelsize=FONT_CONFIG['tick_labels'])

    # 调整布局
    plt.tight_layout()

    # 保存图表到消融实验文件夹（PNG和PDF格式，参考原代码）
    output_folder = '消融实验'
    os.makedirs(output_folder, exist_ok=True)

    png_filename = os.path.join(output_folder, "snr_accuracy_ablation.png")
    plt.savefig(png_filename, dpi=300, bbox_inches='tight')
    print(f"已保存PNG图表: {png_filename}")

    pdf_filename = os.path.join(output_folder, "snr_accuracy_ablation.pdf")
    plt.savefig(pdf_filename, format='pdf', bbox_inches='tight')
    print(f"已保存PDF图表: {pdf_filename}")

    # 关闭图表释放内存
    plt.close()

def main():
    """主函数"""
    print("=" * 60)
    print("消融实验结果提取工具")
    print("=" * 60)

    # 创建SNR准确率表格
    print("  创建SNR准确率表格...")
    snr_df = create_snr_table()

    if snr_df is not None:
        # 保存SNR准确率Excel到消融实验文件夹
        output_folder = '消融实验'
        os.makedirs(output_folder, exist_ok=True)

        snr_excel_filename = os.path.join(output_folder, "消融实验_SNR准确率.xlsx")
        snr_df.to_excel(snr_excel_filename, index=False, engine='openpyxl')
        print(f"  已保存: {snr_excel_filename}")

        # 创建SNR准确率曲线图
        print("  创建SNR准确率曲线图...")
        create_snr_accuracy_plot(snr_df)
    else:
        print(f"  警告: 无法创建消融实验的SNR准确率表格")

    print(f"\n=" * 60)
    print("消融实验结果处理完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
