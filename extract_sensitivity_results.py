#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消融实验结果提取工具
处理消融实验文件夹下的三个模型结果，生成Excel表格和图像
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置区域
BASE_FOLDERS = [
    '消融实验/完整模型',
    '消融实验/单元换LSTM',
    '消融实验/双向LSTM换卷积层'
]

def find_latest_folder(base_path, prefix_filter=None):
    """查找最新的文件夹（按时间戳排序）"""
    if not os.path.exists(base_path):
        return None
    
    folders = []
    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path):
            if prefix_filter is None or item.startswith(prefix_filter):
                folders.append(item)
    
    if not folders:
        return None
    
    # 按时间戳排序，选择最新的
    folders.sort(reverse=True)
    return folders[0]

def extract_training_data(unit_path):
    """从训练文件夹提取模型参数量"""
    unit_name = os.path.basename(unit_path)
    print(f"    正在提取 {unit_name} 的训练数据...")

    params_path = os.path.join(unit_path, '参数')
    if not os.path.exists(params_path):
        print(f"      ❌ 参数文件夹不存在: {params_path}")
        return None

    # 查找训练文件夹（不以test_开头的文件夹）
    training_folder = None
    for item in os.listdir(params_path):
        item_path = os.path.join(params_path, item)
        if os.path.isdir(item_path) and not item.startswith('test_'):
            training_folder = item
            break

    if not training_folder:
        print(f"      ❌ 未找到训练文件夹 (非test_开头的文件夹)")
        return None

    print(f"      📁 找到训练文件夹: {training_folder}")

    # 读取training_summary.json
    training_summary_path = os.path.join(params_path, training_folder, 'results', 'training_summary.json')
    if not os.path.exists(training_summary_path):
        print(f"      ❌ 训练摘要文件不存在: {training_summary_path}")
        return None

    try:
        with open(training_summary_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        total_params = data.get('total_parameters', '')
        trainable_params = data.get('trainable_parameters', '')

        print(f"      ✅ 成功读取训练数据:")
        print(f"         📄 文件: {training_summary_path}")
        print(f"         📊 总参数量: {total_params}")
        print(f"         📊 可训练参数量: {trainable_params}")

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params
        }
    except Exception as e:
        print(f"      ❌ 读取训练摘要文件失败: {training_summary_path}")
        print(f"         错误: {e}")
        return None

def extract_test_data_from_params(unit_path):
    """从参数文件夹的测试文件夹提取MACs和测试时间"""
    unit_name = os.path.basename(unit_path)
    print(f"    正在提取 {unit_name} 的参数测试数据...")

    params_path = os.path.join(unit_path, '参数')
    if not os.path.exists(params_path):
        print(f"      ❌ 参数文件夹不存在: {params_path}")
        return None

    # 查找测试文件夹（以test_开头的文件夹）
    test_folder = find_latest_folder(params_path, 'test_')
    if not test_folder:
        print(f"      ❌ 未找到参数测试文件夹 (test_开头的文件夹)")
        return None

    print(f"      📁 找到参数测试文件夹: {test_folder}")

    # 读取test_summary.json
    test_summary_path = os.path.join(params_path, test_folder, 'results', 'test_summary.json')
    if not os.path.exists(test_summary_path):
        print(f"      ❌ 参数测试摘要文件不存在: {test_summary_path}")
        return None

    try:
        with open(test_summary_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        macs = data.get('model_complexity', {}).get('macs', '')
        macs_raw = data.get('model_complexity', {}).get('macs_raw', '')
        avg_time = data.get('inference_performance', {}).get('avg_inference_time_ms', '')

        print(f"      ✅ 成功读取参数测试数据:")
        print(f"         📄 文件: {test_summary_path}")
        print(f"         📊 MACs: {macs}")
        print(f"         📊 MACs原始值: {macs_raw}")
        print(f"         📊 平均推理时间: {avg_time} ms")

        return {
            'macs': macs,
            'macs_raw': macs_raw,
            'avg_inference_time_ms': avg_time
        }
    except Exception as e:
        print(f"      ❌ 读取参数测试摘要文件失败: {test_summary_path}")
        print(f"         错误: {e}")
        return None

def extract_accuracy_data(unit_path):
    """从正确率文件夹提取准确率、F1、Kappa"""
    unit_name = os.path.basename(unit_path)
    print(f"    正在提取 {unit_name} 的准确率数据...")

    accuracy_path = os.path.join(unit_path, '正确率')
    if not os.path.exists(accuracy_path):
        print(f"      ❌ 正确率文件夹不存在: {accuracy_path}")
        return None

    # 查找测试文件夹（以test_开头的文件夹）
    test_folder = find_latest_folder(accuracy_path, 'test_')
    if not test_folder:
        print(f"      ❌ 未找到正确率测试文件夹 (test_开头的文件夹)")
        return None

    print(f"      📁 找到正确率测试文件夹: {test_folder}")

    # 读取test_summary.json
    test_summary_path = os.path.join(accuracy_path, test_folder, 'results', 'test_summary.json')
    if not os.path.exists(test_summary_path):
        print(f"      ❌ 正确率测试摘要文件不存在: {test_summary_path}")
        return None

    try:
        with open(test_summary_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        accuracy = data.get('overall_metrics', {}).get('accuracy', '')
        macro_f1 = data.get('overall_metrics', {}).get('macro_f1', '')
        kappa = data.get('overall_metrics', {}).get('kappa', '')

        print(f"      ✅ 成功读取准确率数据:")
        print(f"         📄 文件: {test_summary_path}")
        print(f"         📊 准确率: {accuracy}")
        print(f"         📊 Macro-F1: {macro_f1}")
        print(f"         📊 Kappa: {kappa}")

        return {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa
        }
    except Exception as e:
        print(f"      ❌ 读取正确率测试摘要文件失败: {test_summary_path}")
        print(f"         错误: {e}")
        return None

def extract_snr_data(unit_path):
    """从正确率文件夹提取各SNR准确率"""
    unit_name = os.path.basename(unit_path)
    print(f"    正在提取 {unit_name} 的SNR数据...")

    accuracy_path = os.path.join(unit_path, '正确率')
    if not os.path.exists(accuracy_path):
        print(f"      ❌ 正确率文件夹不存在: {accuracy_path}")
        return None

    # 查找测试文件夹（以test_开头的文件夹）
    test_folder = find_latest_folder(accuracy_path, 'test_')
    if not test_folder:
        print(f"      ❌ 未找到正确率测试文件夹 (test_开头的文件夹)")
        return None

    print(f"      📁 找到正确率测试文件夹: {test_folder}")

    # 读取snr_results.json
    snr_results_path = os.path.join(accuracy_path, test_folder, 'plots', 'snr_results.json')
    if not os.path.exists(snr_results_path):
        print(f"      ❌ SNR结果文件不存在: {snr_results_path}")
        return None

    try:
        with open(snr_results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 提取SNR数据，格式为 {SNR值: 准确率}
        snr_data = {}
        snr_count = 0
        for snr_key, metrics in data.items():
            if snr_key.startswith('SNR_'):
                # 提取SNR值，例如从 'SNR_-20dB' 提取 '-20dB'
                snr_value = snr_key.replace('SNR_', '')
                accuracy = metrics.get('accuracy', 0)
                snr_data[snr_value] = accuracy
                snr_count += 1

        print(f"      ✅ 成功读取SNR数据:")
        print(f"         📄 文件: {snr_results_path}")
        print(f"         📊 SNR数据点数量: {snr_count}")
        print(f"         📊 SNR范围: {list(snr_data.keys())}")

        return snr_data

    except Exception as e:
        print(f"      ❌ 读取SNR结果文件失败: {snr_results_path}")
        print(f"         错误: {e}")
        return None

def get_all_snr_values(base_folder):
    """获取所有SNR值的并集"""
    all_snr_values = set()
    
    if not os.path.exists(base_folder):
        return []
    
    for unit_name in os.listdir(base_folder):
        unit_path = os.path.join(base_folder, unit_name)
        if os.path.isdir(unit_path):
            snr_data = extract_snr_data(unit_path)
            if snr_data:
                all_snr_values.update(snr_data.keys())
    
    # 按SNR值排序
    snr_list = list(all_snr_values)
    snr_list.sort(key=lambda x: int(x.replace('dB', '')))
    return snr_list

def create_main_table(base_folder):
    """创建主要指标表格"""
    folder_name = os.path.basename(base_folder)
    print(f"\n  📊 创建 {folder_name} 主要指标表格...")

    if not os.path.exists(base_folder):
        print(f"    ❌ 文件夹不存在: {base_folder}")
        return None

    # 定义列名
    columns = [
        '单元名称',
        '模型参数量',
        'MACs',
        '测试时间(ms)',
        '测试准确率',
        'F1分数',
        'Kappa值'
    ]

    rows = []

    # 获取所有单元/分解层次
    units = []
    for item in os.listdir(base_folder):
        item_path = os.path.join(base_folder, item)
        if os.path.isdir(item_path):
            units.append(item)

    units.sort()  # 按名称排序
    print(f"    📁 找到 {len(units)} 个单元/分解层次: {units}")

    for unit_name in units:
        print(f"\n  🔍 处理 {unit_name}:")
        unit_path = os.path.join(base_folder, unit_name)

        # 提取各种数据
        training_data = extract_training_data(unit_path)
        test_params_data = extract_test_data_from_params(unit_path)
        accuracy_data = extract_accuracy_data(unit_path)

        # 构建行数据
        row = [unit_name]

        # 模型参数量（来自训练文件夹）
        if training_data:
            params = training_data.get('total_parameters', '')
            row.append(params)
            print(f"    ✅ 模型参数量: {params}")
        else:
            row.append('')
            print(f"    ❌ 模型参数量: 未找到")

        # MACs和测试时间（来自参数文件夹的测试文件夹）
        if test_params_data:
            macs = test_params_data.get('macs', '')
            time_ms = test_params_data.get('avg_inference_time_ms', '')
            row.append(macs)
            row.append(time_ms)
            print(f"    ✅ MACs: {macs}")
            print(f"    ✅ 测试时间: {time_ms} ms")
        else:
            row.extend(['', ''])
            print(f"    ❌ MACs: 未找到")
            print(f"    ❌ 测试时间: 未找到")

        # 准确率、F1、Kappa（来自正确率文件夹的测试文件夹）
        if accuracy_data:
            acc = accuracy_data.get('accuracy', '')
            f1 = accuracy_data.get('macro_f1', '')
            kappa = accuracy_data.get('kappa', '')
            row.append(acc)
            row.append(f1)
            row.append(kappa)
            print(f"    ✅ 准确率: {acc}")
            print(f"    ✅ F1分数: {f1}")
            print(f"    ✅ Kappa值: {kappa}")
        else:
            row.extend(['', '', ''])
            print(f"    ❌ 准确率: 未找到")
            print(f"    ❌ F1分数: 未找到")
            print(f"    ❌ Kappa值: 未找到")

        rows.append(row)
        print(f"    📝 {unit_name} 行数据: {row}")

    # 创建DataFrame
    df = pd.DataFrame(rows, columns=columns)
    print(f"\n  ✅ {folder_name} 主要指标表格创建完成，共 {len(df)} 行数据")
    return df

def create_snr_table(base_folder):
    """创建SNR准确率表格"""
    if not os.path.exists(base_folder):
        print(f"警告: 文件夹 {base_folder} 不存在")
        return None
    
    # 获取所有SNR值
    all_snr_values = get_all_snr_values(base_folder)
    if not all_snr_values:
        print(f"警告: 在 {base_folder} 中未找到SNR数据")
        return None
    
    # 创建列名：单元名称 + 所有SNR值
    columns = ['单元名称'] + all_snr_values
    
    rows = []
    
    # 获取所有单元/分解层次
    units = []
    for item in os.listdir(base_folder):
        item_path = os.path.join(base_folder, item)
        if os.path.isdir(item_path):
            units.append(item)
    
    units.sort()  # 按名称排序
    
    for unit_name in units:
        unit_path = os.path.join(base_folder, unit_name)
        
        row = [unit_name]  # 第一列是单元名称
        
        # 提取SNR数据
        snr_data = extract_snr_data(unit_path)
        
        if snr_data:
            # 为每个SNR值填入对应的准确率
            for snr_value in all_snr_values:
                accuracy = snr_data.get(snr_value, '')  # 如果没有该SNR值，填入空字符串
                row.append(accuracy)
        else:
            # 没有SNR数据，所有SNR列填入空字符串
            row.extend([''] * len(all_snr_values))
        
        rows.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(rows, columns=columns)
    return df

def create_snr_accuracy_plot(snr_df, folder_name, base_folder):
    """创建SNR准确率曲线图（完全参考plot_snr_results.py格式）"""
    if snr_df is None or snr_df.empty:
        print(f"警告: {folder_name} 的SNR表格为空，无法创建图表")
        return

    # 设置Times New Roman字体（完全参考原代码）
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['font.size'] = 12

    # 字体大小配置（完全参考原代码）
    FONT_CONFIG = {
        'axis_labels': 20,      # X and Y axis labels
        'legend': 18,           # Legend text
        'tick_labels': 18,      # Tick labels
    }

    # 提取SNR列（排除单元名称列）
    snr_columns = [col for col in snr_df.columns if col != '单元名称']
    if not snr_columns:
        print(f"警告: 在 {folder_name} 中未找到SNR列")
        return

    # 转换SNR值为数值（用于排序和绘图）
    snr_values = []
    for snr_col in snr_columns:
        try:
            # 从 '-20dB' 提取 -20
            snr_num = int(snr_col.replace('dB', ''))
            snr_values.append(snr_num)
        except:
            snr_values.append(0)

    # 按SNR值排序
    sorted_indices = np.argsort(snr_values)
    sorted_snr_values = [snr_values[i] for i in sorted_indices]
    sorted_snr_columns = [snr_columns[i] for i in sorted_indices]

    # 创建图表（IEEE journal格式 4:3比例）
    plt.figure(figsize=(8, 6))

    # 定义颜色和样式（完全参考原代码的PLOT_STYLES）
    plot_styles = [

        {'marker': 'D', 'linestyle': ':', 'color': '#1f77b4'},   # Blue
        {'marker': 's', 'linestyle': '--', 'color': '#ff7f0e'},  # Orange
        {'marker': '<', 'linestyle': '--', 'color': '#2ca02c'},  # Green
        {'marker': '>', 'linestyle': '-.', 'color': '#9467bd'},  # Purple
        {'marker': '^', 'linestyle': '-', 'color': '#8c564b'},   # Brown
        {'marker': 'v', 'linestyle': '-.', 'color': '#e377c2'},  # Pink
        {'marker': 'p', 'linestyle': ':', 'color': '#7f7f7f'},   # Gray
        {'marker': 'o', 'linestyle': '-', 'color': '#d62728'}  # Red
    ]

    for idx, row in snr_df.iterrows():
        unit_name = row['单元名称']

        # 处理图例名称：分解层次用T=数字，单元个数用units=数字
        if '分解' in unit_name:
            # 从"分解1"提取"1"，转换为"T=1"
            try:
                level_num = unit_name.replace('分解', '')
                display_name = f'T={level_num}'
            except:
                display_name = unit_name
        elif '单元' in unit_name:
            # 从"单元1"提取"1"，转换为"Units=1"
            try:
                unit_num = unit_name.replace('单元', '')
                display_name = f'Units={unit_num}'
            except:
                display_name = unit_name
        else:
            display_name = unit_name

        accuracies = []
        valid_snr_values = []

        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            acc = row[snr_col]
            if pd.notna(acc) and acc != '':
                try:
                    # 转换为0-1范围（完全参考原代码格式）
                    acc_decimal = float(acc) / 100.0
                    accuracies.append(acc_decimal)
                    valid_snr_values.append(snr_val)
                except:
                    continue

        if not accuracies:
            print(f"警告: {unit_name} 没有有效的准确率数据")
            continue

        # 获取样式 - 特殊处理T=2和Units=3为红色圆圈
        if display_name == 'T=2' or display_name == 'Units=3':
            style = {'marker': 'o', 'linestyle': '-', 'color': '#d62728'}  # 红色圆圈
        else:
            style = plot_styles[idx % len(plot_styles)]

        # 绘制曲线（完全参考原代码样式）
        plt.plot(valid_snr_values, accuracies,
                marker=style['marker'],
                linestyle=style['linestyle'],
                color=style['color'],
                label=display_name,  # 使用处理后的显示名称
                linewidth=2,
                markersize=6)

    # 设置图表属性（完全参考原代码）
    plt.xlabel('SNR (dB)', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    plt.ylabel('Accuracy', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    # 不设置标题（参考原代码）
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper left', fontsize=FONT_CONFIG['legend'], frameon=True, fancybox=False, shadow=False)

    # 设置y轴格式（完全参考原代码）
    # 根据数据类型设置不同的y轴起始位置
    # 检查数据的最小值来决定起始位置
    all_accuracies = []
    for idx, row in snr_df.iterrows():
        for snr_col in sorted_snr_columns:
            acc = row[snr_col]
            if pd.notna(acc) and acc != '':
                try:
                    acc_decimal = float(acc) / 100.0
                    all_accuracies.append(acc_decimal)
                except:
                    continue

    if all_accuracies:
        min_acc = min(all_accuracies)
        if min_acc > 0.3:  # 如果最小准确率大于0.3，从0.35开始
            plt.ylim(0.35, 1.05)
        else:
            plt.ylim(0, 1.05)
    else:
        plt.ylim(0, 1.05)  # 默认从0开始
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1f}'.format(y)))

    # 设置刻度参数（完全参考原代码）
    plt.tick_params(axis='both', which='major', labelsize=FONT_CONFIG['tick_labels'])

    # 调整布局
    plt.tight_layout()

    # 保存图表到对应文件夹（PNG和PDF格式，参考原代码）
    png_filename = os.path.join(base_folder, f"snr_accuracy_{folder_name}.png")
    plt.savefig(png_filename, dpi=300, bbox_inches='tight')
    print(f"已保存PNG图表: {png_filename}")

    pdf_filename = os.path.join(base_folder, f"snr_accuracy_{folder_name}.pdf")
    plt.savefig(pdf_filename, format='pdf', bbox_inches='tight')
    print(f"已保存PDF图表: {pdf_filename}")

    # 关闭图表释放内存
    plt.close()

def process_folder(base_folder):
    """处理单个文件夹，生成Excel表格和图表"""
    folder_name = os.path.basename(base_folder)
    print(f"\n处理文件夹: {folder_name}")

    # 确保输出文件夹存在
    os.makedirs(base_folder, exist_ok=True)

    # 创建主要指标表格
    print("  创建主要指标表格...")
    main_df = create_main_table(base_folder)

    if main_df is not None:
        # 保存主要指标Excel到对应文件夹
        main_excel_filename = os.path.join(base_folder, f"{folder_name}_主要指标.xlsx")
        main_df.to_excel(main_excel_filename, index=False, engine='openpyxl')
        print(f"  已保存: {main_excel_filename}")
    else:
        print(f"  警告: 无法创建 {folder_name} 的主要指标表格")

    # 创建SNR准确率表格
    print("  创建SNR准确率表格...")
    snr_df = create_snr_table(base_folder)

    if snr_df is not None:
        # 保存SNR准确率Excel到对应文件夹
        snr_excel_filename = os.path.join(base_folder, f"{folder_name}_SNR准确率.xlsx")
        snr_df.to_excel(snr_excel_filename, index=False, engine='openpyxl')
        print(f"  已保存: {snr_excel_filename}")
    else:
        print(f"  警告: 无法创建 {folder_name} 的SNR准确率表格")

    # 创建SNR准确率曲线图
    print("  创建SNR准确率曲线图...")
    create_snr_accuracy_plot(snr_df, folder_name, base_folder)

def main():
    """主函数"""
    print("=" * 60)
    print("敏感试验结果提取工具")
    print("=" * 60)

    for base_folder in BASE_FOLDERS:
        if os.path.exists(base_folder):
            process_folder(base_folder)
        else:
            print(f"\n警告: 文件夹 {base_folder} 不存在，跳过处理")

    print(f"\n=" * 60)
    print("所有敏感试验结果处理完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
